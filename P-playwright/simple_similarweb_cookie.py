#!/usr/bin/env python
# -*- coding: utf-8 -*-

from playwright.sync_api import sync_playwright
import time
import os
from datetime import datetime
import csv
import json  # 添加json导入

# 创建下载目录
# DOWNLOAD_DIR = "downloads"
# os.makedirs(DOWNLOAD_DIR, exist_ok=True)
DOWNLOAD_DIR = os.path.join(os.path.dirname(__file__), 'downloads')
os.makedirs(DOWNLOAD_DIR, exist_ok=True)

# 创建HTML目录
HTML_DIR = os.path.join(os.path.dirname(__file__), 'html')
os.makedirs(HTML_DIR, exist_ok=True)

# 创建screenshots目录
SCREENSHOTS_DIR = os.path.join(os.path.dirname(__file__), 'screenshots')
os.makedirs(SCREENSHOTS_DIR, exist_ok=True)


"""
读取csv文件中的第二列内容，是关键词，存储到数据中，打印输出
"""
def read_keywords_from_csv(csv_file_path):
    """
    读取CSV文件中的第二列内容作为关键词，使用set过滤重复关键词，并打印输出
    
    参数:
        csv_file_path (str): CSV文件的路径
    """
    # 使用set来存储关键词，自动过滤重复项
    keywords_set = set()
    try:
        with open(csv_file_path, 'r', encoding='utf-8') as file:
            csv_reader = csv.reader(file)
            # 跳过标题行（如果有）
            next(csv_reader, None)
            # 读取每一行的第二列（索引为1）
            for row in csv_reader:
                if len(row) > 1:  # 确保行至少有两列
                    keywords_set.add(row[1])
        
        # 转换为列表并排序，便于打印
        keywords_list = sorted(list(keywords_set))
        
        # 打印关键词
        print(f"从CSV文件 {csv_file_path} 中读取了 {len(keywords_list)} 个不重复关键词:")
        return keywords_list
    except Exception as e:
        print(f"读取CSV文件时出错: {e}")
    return None

def load_cookies_from_file(file_path):
    """
    从JSON文件加载cookie
    
    参数:
        file_path (str): Cookie文件的路径
    返回:
        list: cookie列表
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            cookie_data = json.load(f)
            
        # 将字典格式转换为Playwright需要的列表格式
        cookies = []
        for name, value in cookie_data.items():
            cookies.append({
                "name": name,
                "value": str(value),
                "domain": ".semrush.fun",  # 添加适当的域名
                "path": "/"
            })
        print(f"成功从 {file_path} 加载了 {len(cookies)} 个cookie")
        return cookies
    except Exception as e:
        print(f"读取cookie文件时出错: {e}")
        return []

def main():
    """
    使用Playwright打开指定网址，通过cookie认证直接访问关键词魔术工具
    """
    # 关键词魔术工具URL
    keyword_magic_url = "https://vip5.semrush.fun/analytics/keywordmagic/start"
    
    # 要搜索的关键词列表
    csv_file_path = os.path.join(os.path.dirname(__file__), 'word.csv')
    search_keywords = read_keywords_from_csv(csv_file_path)
    
    # 加载cookie文件
    cookie_file_path = os.path.join(os.path.dirname(__file__), 'cookie.json')
    cookies = load_cookies_from_file(cookie_file_path)
    
    print(f"准备使用cookie直接访问关键词魔术工具: {keyword_magic_url}")
    
    with sync_playwright() as p:
        # 启动浏览器
        browser = p.chromium.launch(
            headless=False,  # 显示浏览器界面
            channel="chrome"  # 使用已安装的Chrome浏览器
        )
        
        # 创建新的浏览器上下文，启用下载功能并加载cookie
        context = browser.new_context(
            accept_downloads=True,  # 允许下载
        )
        
        # 设置cookie
        if cookies:
            context.add_cookies(cookies)
            print("已将cookie加载到浏览器")
        else:
            print("警告：未能加载cookie，可能无法正常访问目标网站")
        
        # 创建新页面
        page = context.new_page()
        
        # 设置全局超时时间 - 增加到180秒
        page.set_default_timeout(180000)
        
        try:
            # 直接访问关键词魔术工具URL
            print(f"正在加载关键词魔术工具页面...")
            
            print("开始导航，这可能需要较长时间...")
            # 导航到关键词魔术工具URL，增加超时时间到180秒
            response = page.goto(keyword_magic_url, wait_until="domcontentloaded", timeout=180000)
            
            if response and response.ok:
                print(f"成功加载关键词魔术工具页面，状态码: {response.status}")
            else:
                print(f"页面加载可能有问题，状态码: {response.status if response else '未知'}")
                
            # 等待页面稳定，增加超时时间到90秒
            print("等待页面网络活动稳定，这可能需要较长时间...")
            page.wait_for_load_state("networkidle", timeout=90000)
            print("关键词魔术工具页面网络活动已稳定")
            
            # 等待额外时间确保页面完全加载，增加到5秒
            print("额外等待5秒确保页面完全加载...")
            time.sleep(5)
            print("额外等待完成")
            
            # 关键词魔术工具页面截图
            kmt_screenshot = f"screenshots/kmt_page_{int(time.time())}.png"
            page.screenshot(path=kmt_screenshot)
            print(f"关键词魔术工具页面截图已保存到: {kmt_screenshot}")
            
            # 保存页面HTML以便分析
            try:
                html_path_dir = os.path.join(os.path.dirname(__file__), 'html')
                os.makedirs(html_path_dir, exist_ok=True)   
                html_path = os.path.join(html_path_dir, f"kmt_page_html_{int(time.time())}.html")
                with open(html_path, "w", encoding="utf-8") as f:
                    f.write(page.content())
                print(f"关键词魔术工具页面HTML已保存到: {html_path}")
            except Exception as e:
                print(f"保存HTML时出错: {e}")

            def search_single_keyword(page, keyword):
                """
                搜索单个关键词并导出结果
                Args:
                    page: Playwright页面对象
                    keyword: 要搜索的关键词
                """
                print(f"\n开始搜索关键词: {keyword}")
                
                # 查找关键词输入框并输入关键词
                print(f"正在查找关键词输入框，准备输入: {keyword}")
                
                # 尝试多种可能的选择器来查找关键词输入框
                keyword_input_selectors = [
                    'input[placeholder*="输入关键词"]',
                    'input[placeholder*="Enter keyword"]',
                    'input[type="text"]',
                    'input.search-input',
                    'input.keyword-input',
                    'input[name="keyword"]'
                ]
                
                keyword_input = None
                for selector in keyword_input_selectors:
                    try:
                        if page.locator(selector).count() > 0:
                            keyword_input = page.locator(selector).first
                            break
                    except Exception as e:
                        print(f"尝试选择器 {selector} 时出错: {e}")

                if not keyword_input:
                    print(f"未找到关键词输入框,跳过关键词: {keyword}")
                    return False

                # 清空输入框并输入关键词
                keyword_input.click()
                keyword_input.fill(keyword)
                print(f"已输入关键词: {keyword}")
                
                # 截图
                input_screenshot = f"screenshots/keyword_input_{int(time.time())}.png"
                page.screenshot(path=input_screenshot)
                print(f"输入关键词后截图已保存到: {input_screenshot}")
                
                # 步骤4：查找并点击搜索按钮
                print("正在查找搜索按钮...")
                
                # 尝试多种可能的选择器来查找搜索按钮
                search_button_selectors = [
                    'button:has-text("搜索")',
                    'button:has-text("Search")',
                    'button[type="submit"]',
                    'button.search-button',
                    'input[type="submit"]'
                ]
                
                search_button = None
                for selector in search_button_selectors:
                    if page.locator(selector).count() > 0:
                        search_button = page.locator(selector).first
                        break
                
                try:
                    # 如果没有找到搜索按钮，尝试按回车键
                    if not search_button:
                        print("未找到搜索按钮，尝试按回车键...")
                        keyword_input.press("Enter")
                        print("已按回车键执行搜索")
                    else:
                        print("找到搜索按钮，准备点击...")
                        search_button.click()
                        print("已点击搜索按钮")
                    
                    # 等待搜索结果加载
                    print("等待搜索结果加载，这可能需要较长时间...")
                    page.wait_for_load_state("networkidle", timeout=180000)
                    time.sleep(5)
                    print("搜索结果已加载完成")
                    
                    # 保存搜索结果
                    results_screenshot = f"screenshots/search_results_{int(time.time())}.png"
                    page.screenshot(path=results_screenshot)
                    print(f"搜索结果截图已保存到: {results_screenshot}")

                    # 查找并点击KD%筛选框
                    print("正在查找KD%筛选框...")
                    kd_filter_selectors = [
                        'text="KD %"',
                        'text="KD%"',
                        'text="Keyword Difficulty"',
                        '*:has-text("KD %")',
                        '*:has-text("KD%")'
                    ]
                    
                    kd_filter = None
                    for selector in kd_filter_selectors:
                        if page.locator(selector).count() > 0:
                            kd_filter = page.locator(selector).first
                            break
                    
                    if kd_filter:
                        print("找到KD%筛选框，准备点击...")
                        kd_filter.click()
                        print("已点击KD%筛选框")
                        
                        # 等待筛选选项加载
                        time.sleep(2)
                        
                        # 查找并填写"到"输入框
                        to_input_selectors = [
                            'input[placeholder="到"]',
                            'input[placeholder="To"]',
                            'input.to-input'
                        ]
                        
                        to_input = None
                        for selector in to_input_selectors:
                            if page.locator(selector).count() > 0:
                                to_input = page.locator(selector).first
                                break
                        
                        if to_input:
                            print("找到'到'输入框，准备输入49...")
                            to_input.fill("49")
                            print("已输入49")
                            
                            # 查找并点击应用按钮
                            apply_button_selectors = [
                                'button:has-text("应用")',
                                'button:has-text("Apply")',
                                'button.apply-button'
                            ]
                            
                            apply_button = None
                            for selector in apply_button_selectors:
                                if page.locator(selector).count() > 0:
                                    apply_button = page.locator(selector).first
                                    break
                            
                            if apply_button:
                                print("找到应用按钮，准备点击...")
                                apply_button.click()
                                print("已点击应用按钮")
                                
                                # 等待结果刷新
                                print("等待筛选结果加载...")
                                page.wait_for_load_state("networkidle", timeout=30000)
                                time.sleep(3)
                                print("筛选结果已加载完成")
                                
                                # 保存筛选后的结果截图
                                filtered_screenshot = f"screenshots/filtered_results_{int(time.time())}.png"
                                page.screenshot(path=filtered_screenshot)
                                print(f"筛选后结果截图已保存到: {filtered_screenshot}")
                            else:
                                print("未找到应用按钮")
                        else:
                            print("未找到'到'输入框")
                    else:
                        print("未找到KD%筛选框")

                    # 查找并点击导出按钮
                    print("正在查找导出按钮...")
                    export_button_selectors = [
                        'button:has-text("导出")',
                        'button:has-text("Export")',
                        'a:has-text("导出")',
                        'a:has-text("Export")',
                        'div:has-text("导出")',
                        'div:has-text("Export")'
                    ]
                    
                    export_button = None
                    for selector in export_button_selectors:
                        if page.locator(selector).count() > 0:
                            export_button = page.locator(selector).first
                            break
                    
                    if export_button:
                        print("找到导出按钮，准备点击...")
                        
                        # 准备下载
                        with page.expect_download() as download_info:
                            export_button.click()
                            print("已点击导出按钮")
                            
                            # 等待导出对话框加载
                            time.sleep(2)
                            print("等待导出选项加载...")
                            
                            # 查找并点击CSV按钮
                            csv_button_selectors = [
                                'button:has-text("CSV")',
                                'a:has-text("CSV")',
                                'div:has-text("CSV")',
                                '*:has-text("CSV")'
                            ]
                            
                            csv_button = None
                            for selector in csv_button_selectors:
                                if page.locator(selector).count() > 0:
                                    csv_button = page.locator(selector).first
                                    break
                            
                            if csv_button:
                                print("找到CSV按钮，准备点击...")
                                csv_button.click()
                                print("已点击CSV按钮")
                                # 等待下载开始
                                time.sleep(2)
                            else:
                                print("未找到CSV按钮")
                            
                            # 获取下载信息
                            download = download_info.value
                            
                            # 生成保存文件名
                            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                            save_path = os.path.join(DOWNLOAD_DIR, f"keyword_magic_{keyword}_{timestamp}.csv")
                            
                            # 保存文件
                            print(f"正在保存文件到: {save_path}")
                            download.save_as(save_path)
                            print(f"文件已保存到: {save_path}")
                            
                            # 等待文件保存完成
                            time.sleep(3)
                            print("CSV导出流程已完成")
                    else:
                        print("未找到导出按钮")
                except Exception as e:
                    print(f"搜索和导出过程中出错: {e}")
                return True

            try:
                # 循环搜索每个关键词
                for keyword in search_keywords:
                    success = search_single_keyword(page, keyword)
                    if success:
                        print(f"关键词 {keyword} 搜索完成")
                        # 每个关键词搜索之间等待一段时间
                        time.sleep(5)  # 可以根据需要调整等待时间
                    else:
                        print(f"关键词 {keyword} 搜索失败")
            except Exception as e:
                print(f"搜索过程中出错: {e}")
        except KeyboardInterrupt:
            print("用户终止程序")
        except Exception as e:
            print(f"发生错误: {e}")
            import traceback
            traceback.print_exc()
            
            # 保存页面HTML以便分析
            try:
                error_screenshot = f"screenshots/error_screenshot_{int(time.time())}.png"
                page.screenshot(path=error_screenshot)
                print(f"错误页面截图已保存到: {error_screenshot}")
                
                html_path = f"error_page_html_{int(time.time())}.html"
                with open(html_path, "w", encoding="utf-8") as f:
                    f.write(page.content())
                print(f"错误页面HTML已保存到: {html_path}")
            except Exception as save_error:
                print(f"保存错误信息时出错: {save_error}")
        finally:
            try:
                # 关闭浏览器
                browser.close()
                print("浏览器已关闭")
            except Exception as close_error:
                print(f"关闭浏览器时出错: {close_error}")

if __name__ == "__main__":
    main() 